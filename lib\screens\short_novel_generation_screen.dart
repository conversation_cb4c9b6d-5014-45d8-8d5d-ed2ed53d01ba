import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/short_novel_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/short_novel_memory.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/widgets/themed_dropdown.dart';

class ShortNovelGenerationScreen extends StatefulWidget {
  @override
  _ShortNovelGenerationScreenState createState() => _ShortNovelGenerationScreenState();
}

class _ShortNovelGenerationScreenState extends State<ShortNovelGenerationScreen> {
  final ShortNovelController controller = Get.put(ShortNovelController());
  final NovelController novelController = Get.find<NovelController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('短篇小说生成'),
        actions: [
          Obx(() {
            if (controller.currentStage.value != ShortNovelGenerationStage.input &&
                controller.currentStage.value != ShortNovelGenerationStage.completed) {
              return IconButton(
                icon: Icon(Icons.refresh),
                onPressed: controller.regenerateCurrentStep,
                tooltip: '重新生成当前步骤',
              );
            }
            return SizedBox.shrink();
          }),
          IconButton(
            icon: Icon(Icons.home),
            onPressed: () {
              controller.resetGeneration();
              Get.back();
            },
            tooltip: '返回首页',
          ),
        ],
      ),
      body: Obx(() {
        switch (controller.currentStage.value) {
          case ShortNovelGenerationStage.input:
            return _buildInputForm();
          case ShortNovelGenerationStage.generatingWorldView:
            return _buildGenerationProgress('正在生成世界观...');
          case ShortNovelGenerationStage.editingWorldView:
            return _buildWorldViewEditor();
          case ShortNovelGenerationStage.generatingCharacterArcs:
            return _buildGenerationProgress('正在生成角色发展脉络...');
          case ShortNovelGenerationStage.editingCharacterArcs:
            return _buildCharacterArcsEditor();
          case ShortNovelGenerationStage.generatingOutline:
            return _buildGenerationProgress('正在生成详细大纲...');
          case ShortNovelGenerationStage.editingOutline:
            return _buildOutlineEditor();
          case ShortNovelGenerationStage.generatingContent:
            return _buildContentGenerationProgress();
          case ShortNovelGenerationStage.completed:
            return _buildCompletionView();
          case ShortNovelGenerationStage.error:
            return _buildErrorView();
        }
      }),
    );
  }

  Widget _buildInputForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '短篇小说生成设置',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: 16),
          
          Text('小说标题'),
          SizedBox(height: 8),
          Obx(() => TextFormField(
            initialValue: novelController.title.value,
            onChanged: (value) => novelController.title.value = value,
            decoration: InputDecoration(
              hintText: '请输入小说标题',
              border: OutlineInputBorder(),
            ),
          )),
          SizedBox(height: 16),

          Text('选择字数'),
          SizedBox(height: 8),
          Obx(() => DropdownButtonFormField<ShortNovelWordCount>(
            value: novelController.shortNovelWordCount.value,
            decoration: InputDecoration(
              border: OutlineInputBorder(),
            ),
            items: ShortNovelWordCount.values.map((wordCount) =>
              DropdownMenuItem(
                value: wordCount,
                child: Text(wordCount.displayName),
              ),
            ).toList(),
            onChanged: (value) {
              if (value != null) {
                novelController.setShortNovelWordCount(value);
              }
            },
          )),
          SizedBox(height: 16),

          Text('故事背景'),
          SizedBox(height: 8),
          Obx(() => TextFormField(
            initialValue: novelController.background.value,
            onChanged: (value) => novelController.background.value = value,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '请描述故事的背景设定',
              border: OutlineInputBorder(),
            ),
          )),
          SizedBox(height: 16),

          Text('其他要求'),
          SizedBox(height: 8),
          Obx(() => TextFormField(
            initialValue: novelController.otherRequirements.value,
            onChanged: (value) => novelController.otherRequirements.value = value,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '请描述您对小说的特殊要求',
              border: OutlineInputBorder(),
            ),
          )),
          SizedBox(height: 24),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _canStartGeneration() ? _startGeneration : null,
              child: Text('开始生成'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerationProgress(String message) {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          LinearProgressIndicator(),
          SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 16),
          Expanded(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Obx(() => SingleChildScrollView(
                child: Text(
                  controller.realtimeOutput.value,
                  style: TextStyle(fontFamily: 'monospace'),
                ),
              )),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorldViewEditor() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '世界观设定',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          Text(
            '请检查并编辑生成的世界观设定，确认后将生成角色发展脉络',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: 16),
          Expanded(
            child: TextField(
              controller: TextEditingController(text: controller.editableWorldView.value),
              onChanged: (value) => controller.editableWorldView.value = value,
              maxLines: null,
              expands: true,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: '世界观设定内容...',
              ),
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: controller.regenerateCurrentStep,
                  child: Text('重新生成'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: controller.confirmWorldViewAndGenerateCharacters,
                  child: Text('确认并继续'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterArcsEditor() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '角色发展脉络',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          Text(
            '请检查并编辑生成的角色发展脉络，确认后将生成详细大纲',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: 16),
          Expanded(
            child: TextField(
              controller: TextEditingController(text: controller.editableCharacterArcs.value),
              onChanged: (value) => controller.editableCharacterArcs.value = value,
              maxLines: null,
              expands: true,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: '角色发展脉络内容...',
              ),
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: controller.regenerateCurrentStep,
                  child: Text('重新生成'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: controller.confirmCharacterArcsAndGenerateOutline,
                  child: Text('确认并继续'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOutlineEditor() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '详细大纲',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          Text(
            '请检查并编辑生成的详细大纲，确认后将开始生成小说内容',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: 16),
          Expanded(
            child: TextField(
              controller: TextEditingController(text: controller.editableOutline.value),
              onChanged: (value) => controller.editableOutline.value = value,
              maxLines: null,
              expands: true,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: '详细大纲内容...',
              ),
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: controller.regenerateCurrentStep,
                  child: Text('重新生成'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: controller.confirmOutlineAndStartGeneration,
                  child: Text('确认并开始生成'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContentGenerationProgress() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Obx(() => LinearProgressIndicator(value: controller.progress.value)),
          SizedBox(height: 16),
          Obx(() => Text(
            controller.generationStatus.value,
            style: Theme.of(context).textTheme.titleMedium,
          )),
          SizedBox(height: 16),
          Expanded(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Obx(() => SingleChildScrollView(
                child: Text(
                  controller.realtimeOutput.value,
                  style: TextStyle(fontFamily: 'monospace'),
                ),
              )),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionView() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            size: 64,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            '短篇小说生成完成！',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: 8),
          Obx(() => Text(
            '《${controller.memory.value?.title ?? ''}》已保存到我的书库',
            style: Theme.of(context).textTheme.bodyLarge,
          )),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    controller.resetGeneration();
                  },
                  child: Text('生成新的短篇小说'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: Text('返回首页'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            size: 64,
            color: Colors.red,
          ),
          SizedBox(height: 16),
          Text(
            '生成过程中出现错误',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: 8),
          Obx(() => Text(
            controller.generationStatus.value,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          )),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: controller.regenerateCurrentStep,
                  child: Text('重试'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    controller.resetGeneration();
                  },
                  child: Text('重新开始'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  bool _canStartGeneration() {
    return novelController.title.value.isNotEmpty &&
           novelController.background.value.isNotEmpty;
  }

  void _startGeneration() {
    controller.startGeneration(
      title: novelController.title.value,
      genres: novelController.selectedGenres,
      background: novelController.background.value,
      otherRequirements: novelController.otherRequirements.value,
      targetReader: novelController.targetReader.value,
      targetWordCount: novelController.shortNovelWordCount.value.count,
      characterCards: _getSelectedCharacterCardNames(),
      characterTypes: novelController.selectedCharacterTypes.map((t) => t.name).toList(),
    );
  }

  List<String> _getSelectedCharacterCardNames() {
    final List<String> names = [];
    for (final entry in novelController.selectedCharacterCards.entries) {
      for (final card in entry.value) {
        names.add(card.name);
      }
    }
    return names;
  }
}
